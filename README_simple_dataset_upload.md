# Simple Dataset Upload Script

This script demonstrates how to upload a dataset with tags to Braintrust using the Python SDK.

## What this script does

- Creates a dataset called "sample-qa-dataset" in the "pedro-project1" project
- Uploads 6 sample question-answer records with various tags
- Each record includes:
  - `input`: The question or input data
  - `expected`: The expected answer or output
  - `tags`: List of tags for categorization (e.g., "geography", "math", "science")
  - `metadata`: Additional information like difficulty level and category

## Prerequisites

1. **Install the Braintrust Python SDK:**
   ```bash
   pip install braintrust
   ```

2. **Set your Braintrust API key:**
   ```bash
   export BRAINTRUST_API_KEY=your_api_key_here
   ```
   
   You can get your API key from: https://www.braintrust.dev/app/settings

## Running the script

```bash
python simple_dataset_upload.py
```

## Sample data included

The script uploads these sample records with tags:

1. **Geography question** - Tags: `["geography", "capitals", "europe"]`
2. **Math question** - Tags: `["math", "arithmetic", "basic"]`
3. **Chemistry question** - Tags: `["science", "chemistry", "molecules"]`
4. **Literature question** - Tags: `["literature", "shakespeare", "plays"]`
5. **Astronomy question** - Tags: `["astronomy", "planets", "solar-system"]`
6. **Programming question** - Tags: `["programming", "java", "technology"]`

## Customizing the script

You can modify the script to:

- Change the project name (currently "pedro-project1")
- Change the dataset name (currently "sample-qa-dataset")
- Add your own sample data in the `create_sample_data()` function
- Modify tags and metadata as needed

## Expected output

When you run the script, you should see output like:

```
🚀 Braintrust Dataset Upload Script
============================================================
Creating dataset 'sample-qa-dataset' in project 'pedro-project1'...
Generated 6 sample records
✅ Dataset created successfully!
   Dataset ID: [dataset-id]
   Dataset name: sample-qa-dataset
   Project: pedro-project1

Uploading 6 records to dataset...

✅ Record 1/6 uploaded
   ID: [record-id]
   Input: What is the capital of France?
   Expected: Paris
   Tags: [geography, capitals, europe]
   Category: world-knowledge

[... more records ...]

============================================================
🎉 Upload completed!
   Successfully uploaded 6 out of 6 records
   Project: pedro-project1
   Dataset: sample-qa-dataset
   Check your Braintrust dashboard to view the dataset
============================================================
```

## Viewing your dataset

After running the script, you can view your dataset in the Braintrust dashboard:
1. Go to https://www.braintrust.dev/app
2. Navigate to the "pedro-project1" project
3. Click on the "Datasets" tab
4. Find your "sample-qa-dataset" dataset

The tags will be visible in the dataset view and can be used for filtering and organizing your data.
