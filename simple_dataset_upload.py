#!/usr/bin/env python3
"""
Simple script to upload a dataset with tags to Braintrust using the Python SDK.

This script creates a sample dataset in the "pedro-project1" project with various
records that include tags for categorization.
"""

import os
import sys
from braintrust import init_dataset


def create_sample_data():
    """
    Create sample data records with tags for the dataset.
    
    Returns:
        List of dictionaries containing sample records with input, expected, tags, and metadata
    """
    sample_records = [
        {
            "input": "What is the capital of France?",
            "expected": "Paris",
            "tags": ["geography", "capitals", "europe"],
            "metadata": {
                "difficulty": "easy",
                "category": "world-knowledge",
                "source": "general-knowledge"
            }
        },
        {
            "input": "What is 2 + 2?",
            "expected": "4",
            "tags": ["math", "arithmetic", "basic"],
            "metadata": {
                "difficulty": "easy",
                "category": "mathematics",
                "operation": "addition"
            }
        },
        {
            "input": "What is the chemical symbol for water?",
            "expected": "H2O",
            "tags": ["science", "chemistry", "molecules"],
            "metadata": {
                "difficulty": "medium",
                "category": "chemistry",
                "type": "chemical-formula"
            }
        },
        {
            "input": "Who wrote '<PERSON> and Juliet'?",
            "expected": "<PERSON>",
            "tags": ["literature", "shakespeare", "plays"],
            "metadata": {
                "difficulty": "easy",
                "category": "literature",
                "period": "renaissance"
            }
        },
        {
            "input": "What is the largest planet in our solar system?",
            "expected": "Jupiter",
            "tags": ["astronomy", "planets", "solar-system"],
            "metadata": {
                "difficulty": "medium",
                "category": "astronomy",
                "type": "planetary-science"
            }
        },
        {
            "input": "What programming language is known for 'write once, run anywhere'?",
            "expected": "Java",
            "tags": ["programming", "java", "technology"],
            "metadata": {
                "difficulty": "medium",
                "category": "computer-science",
                "domain": "programming-languages"
            }
        }
    ]
    
    return sample_records


def upload_dataset_to_braintrust(
    project_name: str = "pedro-project1",
    dataset_name: str = "sample-qa-dataset",
    description: str = "A sample question-answer dataset with tags for demonstration"
):
    """
    Upload sample dataset to Braintrust.
    
    Args:
        project_name: Name of the Braintrust project
        dataset_name: Name of the dataset to create
        description: Description of the dataset
    """
    
    print(f"Creating dataset '{dataset_name}' in project '{project_name}'...")
    
    # Create sample data
    records = create_sample_data()
    print(f"Generated {len(records)} sample records")
    
    # Initialize Braintrust dataset
    try:
        dataset = init_dataset(
            project=project_name,
            name=dataset_name,
            description=description,
            use_output=False  # Use modern format with 'expected' field
        )
        
        print(f"✅ Dataset created successfully!")
        print(f"   Dataset ID: {dataset.id}")
        print(f"   Dataset name: {dataset.name}")
        print(f"   Project: {dataset.project.name}")
        print()
        
    except Exception as e:
        print(f"❌ Error creating dataset: {e}")
        sys.exit(1)
    
    # Upload records to dataset
    print(f"Uploading {len(records)} records to dataset...")
    print()
    
    uploaded_count = 0
    for i, record in enumerate(records, 1):
        try:
            record_id = dataset.insert(**record)
            uploaded_count += 1
            
            # Display record info
            tags_str = ", ".join(record["tags"])
            print(f"✅ Record {i}/{len(records)} uploaded")
            print(f"   ID: {record_id}")
            print(f"   Input: {record['input']}")
            print(f"   Expected: {record['expected']}")
            print(f"   Tags: [{tags_str}]")
            print(f"   Category: {record['metadata'].get('category', 'N/A')}")
            print()
            
        except Exception as e:
            print(f"❌ Error uploading record {i}: {e}")
            continue
    
    # Flush the dataset to ensure all records are uploaded
    print("🔄 Flushing dataset to ensure all records are uploaded...")
    try:
        dataset.flush()
        print("✅ Dataset flushed successfully!")
    except Exception as e:
        print(f"⚠️  Warning: Error flushing dataset: {e}")

    print("=" * 60)
    print(f"🎉 Upload completed!")
    print(f"   Successfully uploaded {uploaded_count} out of {len(records)} records")
    print(f"   Project: {project_name}")
    print(f"   Dataset: {dataset_name}")
    print(f"   Check your Braintrust dashboard to view the dataset")
    print("=" * 60)


def main():
    """Main function to run the upload script."""
    
    # Check for API key
    if not os.getenv('BRAINTRUST_API_KEY'):
        print("❌ Error: BRAINTRUST_API_KEY environment variable not set")
        print()
        print("Please set your Braintrust API key:")
        print("   export BRAINTRUST_API_KEY=your_api_key_here")
        print()
        print("You can get your API key from: https://www.braintrust.dev/app/settings")
        sys.exit(1)
    
    print("🚀 Braintrust Dataset Upload Script")
    print("=" * 60)
    
    # Upload the dataset to Braintrust
    upload_dataset_to_braintrust()


if __name__ == "__main__":
    main()
