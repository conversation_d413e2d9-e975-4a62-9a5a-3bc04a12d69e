interactions:
- request:
    body: '{"input":"What is 10 + 10?","instructions":"Respond with just the number","model":"gpt-4o-mini"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '96'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.99.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.99.1
      x-stainless-raw-response:
      - 'true'
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.13.3
    method: POST
    uri: https://api.openai.com/v1/responses
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3RTy27jMAy85ysEnZuFH4md5DN6LRYGLdOJWlk0JKrboMi/LyzHjr2bXgJnhhyS
        Q+p7I4TUjTwJ6dD3VXFQed6kxb7M1SE9JpAmRbHLoEmLvNxnmKTHPMnKgzqqIk32rXwZBKh+R8WT
        CFmPI64cAmNTwcCl5b4ssn2RZZHzDBz8kKOo6w0yNmNSDerj7CjYoasWjMcIo3Pk5EnYYEwEtJ0S
        qwYZtPFr1rMLijXZWOQ1NtaIP5ov4j14FnxBYUNXoxvrdvBVUeA+cMX0gXYlN5BMZCoFZl2oowbN
        UOHc83ZH205bvc2SbLdNym16uBsUdeVJvG2EEOI7/s7Od/48GV8m+XE3GF+neVukRYlY1qrF3VPj
        owZfe4wq6D2c8UH85HAkFVlG+2hp2dZKdrIEv3jOjgFgLTFMBr/9XpGGzr2j+gkThU5CZomc4dv9
        a46UjkysDt5rz2B5DB4CY5DswYExaNZbYRfGa+kdfmoKvpoOsopWz1vrHXU9VwrUBasPvC45h+DJ
        anuWp7slEtuWHC+CBntD14GbMjdC3Ma7hhb5WukGLetW4+pmPbpPrbDiEZcNthDMaKz0TA6XQzB2
        PTrgEOH0V3JHo4H3zlpyHTz+LxYX40bX7h1/oqvJa76O59Lo0Mm579HHC2k1Gh+Y5Ew89iiZ+mqx
        3WQG+2WPLlgVbyNOqT3UZnrfIV7pPIC2qxeX7V7+xxcPfB4zrq55JCarUf99yNkz/JnsvPyflJkY
        zEK4mB0Mfr3sDhkaYBjkb5vbXwAAAP//AwDbGb2LawUAAA==
    headers:
      CF-RAY:
      - 97da35698c67169a-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Sep 2025 21:20:23 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=COKui1p1PyfxAaTpUl7QpKysrbwhNGcDSQhVcne3kn0-1757625623-*******-cotK5hjBTwbR36WqOUJtnNS32IaMxlR1lVgkHWREnQpM0BNnnY06onyOG6.5XAD9EJC_HNH95gTjB64Yo69SXnEAqWWTTm1NatHsysUshwg;
        path=/; expires=Thu, 11-Sep-25 21:50:23 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=6CYlt5ha6B9yTDGHSh9xg5_n8WVEqvDkNGxq6bU10GM-1757625623177-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - braintrust-data
      openai-processing-ms:
      - '817'
      openai-project:
      - proj_vsCSXafhhByzWOThMrJcZiw9
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-envoy-upstream-service-time:
      - '819'
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999957'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_11fd255251fb3f822752f773e86a04f3
    status:
      code: 200
      message: OK
- request:
    body: '{"input":"What is 15 + 15?","model":"gpt-4o-mini","text":{"format":{"type":"json_schema","strict":true,"name":"SimpleAnswer","schema":{"properties":{"value":{"title":"Value","type":"integer"}},"required":["value"],"title":"SimpleAnswer","type":"object","additionalProperties":false}}}}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '285'
      content-type:
      - application/json
      cookie:
      - __cf_bm=COKui1p1PyfxAaTpUl7QpKysrbwhNGcDSQhVcne3kn0-1757625623-*******-cotK5hjBTwbR36WqOUJtnNS32IaMxlR1lVgkHWREnQpM0BNnnY06onyOG6.5XAD9EJC_HNH95gTjB64Yo69SXnEAqWWTTm1NatHsysUshwg;
        _cfuvid=6CYlt5ha6B9yTDGHSh9xg5_n8WVEqvDkNGxq6bU10GM-1757625623177-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.99.1
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.99.1
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.13.3
    method: POST
    uri: https://api.openai.com/v1/responses
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAAwAAAP//dFTBjtowEL3zFZbPSxUSIIFb/6BSpV52q2iwJ6x3HTu1x7Roxb9XcSBx
        WPaC4L2ZYfzmzXwsGONK8j3jDn1XbytRFHJVilxm1Wonq2Ytm7xois02F+tMZFDkctMcqnW1yrAo
        +FNfwB7eUNCtiDUeB1w4BEJZQ8+tyk25zTfbvIicJ6Dg+xxh204joRySDiDej84G03fVgPYYYXTO
        Or5nJmgdAWVuibVEAqX9nPXkgiBlzQxv4V9tA3WBarLv+Jkka3UtQM/LtVai7ps9drRc22WrjFrm
        Wb5eZuVyVV1liHX5nj0vGGPsI36O+rb+eJO32oGsenl3sNs15a5CKDdZucsfyhtr0LnDWAW9hyNO
        xFc6RlJYQ2imltK2ZmVvkuA/GrNjABhjCW4yPv+ekdoeO2cPD5hYaM/4xws/gQ74wvdFduFjyOX6
        bczizurYCXivPIGhIbgPjEG8Awdao55PiFwY/NE5PCkbfH2zYB1lHyfYOdt2VAsQr1i/4znlHIK3
        Rpkj31/l4dg01lES1Esd2hbcLXPB2GVwMjRI51pJNKQahTOXenQnJbCmAecSGwh6EJl7sg7TRxC2
        HTqgEOHVt+yKRjGvnTXWtTD9Tob45q2pvXjFFiYLSPTCqa6f4Ow1jHEDbcz7qXrjfDf+L7rEWEOh
        fWKYXsQOHSn0M5yxYcp3YN+aomGsvyL/dMdeG1eG8IiOJ+xlssqUxB3+CcqhnBl6/PcRSbw4dfDw
        lekKDEcsYUBK1esG+kf67niTFnfNcU9OxRvYD3ORcPyE7mC9ovOwv1KFlo/mGcz8apUY3B/I8pGY
        FouT7epk3bIR7FKjuGAEXEfNpfJw0LezGuLZGF2kzOwErsunz3hyV0evxf2RU2I2e+r9Zd0+wh+V
        HTfwq8pkCfREbopRweDnG9cigQSKzr0sLv8BAAD//wMAZ4DdGeIGAAA=
    headers:
      CF-RAY:
      - 97da3571edd9169a-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Sep 2025 21:20:25 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - braintrust-data
      openai-processing-ms:
      - '1229'
      openai-project:
      - proj_vsCSXafhhByzWOThMrJcZiw9
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-envoy-upstream-service-time:
      - '1233'
      x-ratelimit-limit-requests:
      - '30000'
      x-ratelimit-limit-tokens:
      - '150000000'
      x-ratelimit-remaining-requests:
      - '29999'
      x-ratelimit-remaining-tokens:
      - '149999932'
      x-ratelimit-reset-requests:
      - 2ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_83645f5a51f5a71a48ecd36981925921
    status:
      code: 200
      message: OK
version: 1
