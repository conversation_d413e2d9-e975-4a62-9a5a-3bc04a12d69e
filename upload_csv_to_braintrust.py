#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to upload CSV data to Braintrust dataset using the Python SDK.

This script reads the longmemeval_first_10_matched.csv file and creates a dataset
in Braintrust with the data.
"""

import csv
import json
import os
import sys
from typing import Any, Dict, List, Optional

from braintrust import init_dataset

# Increase CSV field size limit to handle large fields
csv.field_size_limit(sys.maxsize)


def parse_json_field(field_value: str) -> Any:
    """
    Parse a JSON string field, handling potential parsing errors.
    
    Args:
        field_value: String that should contain JSON data
        
    Returns:
        Parsed JSON object or the original string if parsing fails
    """
    if not field_value or field_value.strip() == "":
        return None
    
    try:
        return json.loads(field_value)
    except json.JSONDecodeError as e:
        print(f"Warning: Failed to parse JSON field: {e}")
        print(f"Field content: {field_value[:100]}...")
        return field_value  # Return as string if JSON parsing fails


def parse_csv_row(row: Dict[str, str]) -> Dict[str, Any]:
    """
    Parse a single CSV row into a Braintrust dataset record format.
    
    Args:
        row: Dictionary representing a CSV row
        
    Returns:
        Dictionary formatted for Braintrust dataset insertion
    """
    # Parse the input field (which contains JSON)
    input_data = parse_json_field(row.get('input', ''))
    
    # Parse expected output
    expected_data = parse_json_field(row.get('expected', ''))
    
    # Parse tags (comma-separated string to list)
    tags_str = row.get('tags', '')
    tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()] if tags_str else []
    
    # Parse metadata (JSON string)
    metadata = parse_json_field(row.get('metadata', '{}'))
    if not isinstance(metadata, dict):
        metadata = {}
    
    # Add additional CSV fields to metadata
    if row.get('created'):
        metadata['created'] = row['created']
    if row.get('span_id'):
        metadata['span_id'] = row['span_id']
    if row.get('__bt_assignments'):
        metadata['__bt_assignments'] = row['__bt_assignments']
    
    # Create the record
    record = {
        'input': input_data,
        'expected': expected_data,
        'tags': tags,
        'metadata': metadata
    }
    
    return record


def upload_csv_to_braintrust(
    csv_file_path: str,
    project_name: str = "microsoft-longmemeval",
    dataset_name: str = "longmemeval-first-10-matched",
    description: str = "LongMemEval dataset - first 10 matched records from Microsoft reproduction"
) -> None:
    """
    Upload CSV data to Braintrust dataset.
    
    Args:
        csv_file_path: Path to the CSV file
        project_name: Name of the Braintrust project
        dataset_name: Name of the dataset to create
        description: Description of the dataset
    """
    
    # Check if file exists
    if not os.path.exists(csv_file_path):
        print(f"Error: CSV file not found at {csv_file_path}")
        sys.exit(1)
    
    print(f"Reading CSV file: {csv_file_path}")
    
    # Read and parse CSV file
    records = []
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            
            for row_num, row in enumerate(csv_reader, start=1):
                try:
                    record = parse_csv_row(row)
                    records.append(record)
                    print(f"Processed row {row_num}")
                except Exception as e:
                    print(f"Error processing row {row_num}: {e}")
                    continue
                    
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        sys.exit(1)
    
    print(f"Successfully parsed {len(records)} records from CSV")
    
    # Initialize Braintrust dataset
    print(f"Creating Braintrust dataset: {dataset_name} in project: {project_name}")
    
    try:
        dataset = init_dataset(
            project=project_name,
            name=dataset_name,
            description=description,
            use_output=False  # Use modern format with 'expected' field
        )
        
        print(f"Dataset created successfully!")
        print(f"Dataset ID: {dataset.id}")
        print(f"Dataset name: {dataset.name}")
        print(f"Project: {dataset.project.name}")
        
    except Exception as e:
        print(f"Error creating dataset: {e}")
        sys.exit(1)
    
    # Upload records to dataset
    print(f"Uploading {len(records)} records to dataset...")
    
    uploaded_count = 0
    for i, record in enumerate(records):
        try:
            record_id = dataset.insert(**record)
            uploaded_count += 1
            print(f"Uploaded record {i+1}/{len(records)} with ID: {record_id}")
            
        except Exception as e:
            print(f"Error uploading record {i+1}: {e}")
            continue
    
    print(f"\nUpload completed!")
    print(f"Successfully uploaded {uploaded_count} out of {len(records)} records")
    print(f"Dataset URL: Check your Braintrust dashboard for the dataset")


def main():
    """Main function to run the upload script."""
    
    # Check for API key
    if not os.getenv('BRAINTRUST_API_KEY'):
        print("Error: BRAINTRUST_API_KEY environment variable not set")
        print("Please set your Braintrust API key:")
        print("export BRAINTRUST_API_KEY=your_api_key_here")
        sys.exit(1)
    
    # Default CSV file path (relative to current directory)
    csv_file_path = "microsoft_repro/longmemeval_first_10_matched.csv"
    
    # Allow command line argument for CSV file path
    if len(sys.argv) > 1:
        csv_file_path = sys.argv[1]
    
    print("=== Braintrust CSV Upload Script ===")
    print(f"CSV file: {csv_file_path}")
    
    # Upload the CSV to Braintrust
    upload_csv_to_braintrust(csv_file_path)


if __name__ == "__main__":
    main()
